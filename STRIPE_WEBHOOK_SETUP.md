# Stripe Webhook Setup Guide

This guide explains how to configure Stripe webhooks for your license system to automatically process payments and activate licenses.

## Overview

The webhook system handles the following Stripe events:
- `payment_intent.succeeded` - Activates licenses and processes device expansions
- `payment_intent.payment_failed` - Marks payments as failed and logs errors
- `checkout.session.completed` - Updates checkout session information
- `payment_intent.canceled` - Marks payments as canceled
- `invoice.payment_succeeded` - For future subscription support
- `invoice.payment_failed` - For future subscription support

## Environment Variables

Add these environment variables to your `.env` file:

```bash
# Stripe Configuration
STRIPE_SECRET_KEY=sk_test_... # Your Stripe secret key (test or live)
STRIPE_WEBHOOK_SECRET=whsec_... # Webhook endpoint secret from Stripe dashboard

# Optional: For development
CORS_ORIGIN=http://localhost:3000 # Your frontend URL
```

## Stripe Dashboard Configuration

### 1. Create a Webhook Endpoint

1. Log in to your [Stripe Dashboard](https://dashboard.stripe.com/)
2. Navigate to **Developers** → **Webhooks**
3. Click **Add endpoint**
4. Enter your webhook URL:
   - **Development**: `https://your-ngrok-url.ngrok.io/webhooks/stripe`
   - **Production**: `https://your-domain.com/webhooks/stripe`

### 2. Select Events to Send

Select the following events:

**Payment Events:**
- `payment_intent.succeeded`
- `payment_intent.payment_failed`
- `payment_intent.canceled`

**Checkout Events:**
- `checkout.session.completed`

**Invoice Events (for future subscription support):**
- `invoice.payment_succeeded`
- `invoice.payment_failed`

### 3. Get the Webhook Secret

1. After creating the webhook, click on it to view details
2. In the **Signing secret** section, click **Reveal**
3. Copy the secret (starts with `whsec_`)
4. Add it to your `.env` file as `STRIPE_WEBHOOK_SECRET`

## Development Setup

### Using ngrok for Local Development

1. Install ngrok: `npm install -g ngrok` or download from [ngrok.com](https://ngrok.com/)
2. Start your server: `bun run dev`
3. In another terminal, expose your local server: `ngrok http 3000`
4. Use the ngrok HTTPS URL in your Stripe webhook configuration

### Testing Webhooks Locally

1. Use Stripe CLI for local testing:
   ```bash
   stripe listen --forward-to localhost:3000/webhooks/stripe
   ```

2. The CLI will provide a webhook secret starting with `whsec_`
3. Use this secret in your local `.env` file

## Webhook URL Format

Your webhook endpoint will be available at:
```
POST /webhooks/stripe
```

**Full URLs:**
- Development: `http://localhost:3000/webhooks/stripe`
- Production: `https://your-domain.com/webhooks/stripe`

## Webhook Processing Flow

1. **Signature Verification**: Validates the webhook came from Stripe
2. **Idempotency Check**: Prevents duplicate processing of the same event
3. **Event Recording**: Logs the event in the database
4. **Event Processing**: Handles the specific event type
5. **Status Update**: Marks the event as processed or failed

## Event Handlers

### payment_intent.succeeded
- Updates payment status to "SUCCEEDED"
- Creates and activates licenses for license purchases
- Processes device expansions
- Creates audit log entries

### payment_intent.payment_failed
- Updates payment status to "FAILED"
- Logs failure details
- Creates audit log entries

### checkout.session.completed
- Updates checkout session ID in payment record
- Creates audit log entries

## Error Handling and Retries

The system includes robust error handling:

### Automatic Retries
- Failed events are automatically retried up to 3 times
- Retry delays: 5 seconds, 30 seconds, 5 minutes
- Events that exceed max retries are marked as permanently failed

### Manual Retry
You can manually retry failed events using the webhook retry service:

```typescript
import { webhookRetryService } from './services/webhook-retry';

// Retry a specific event
await webhookRetryService.manualRetry('webhook_event_id');

// Get webhook statistics
const stats = await webhookRetryService.getWebhookStats();
```

### Monitoring
- All webhook events are logged with detailed information
- Processing times are tracked
- Error messages and stack traces are preserved
- Audit logs track all payment-related actions

## Cron Job for Retries

Set up a cron job to automatically retry failed webhooks:

```bash
# Add to crontab (crontab -e)
*/5 * * * * cd /path/to/your/app && bun run src/scripts/webhook-retry-cron.ts
```

Or use a process manager like PM2:

```bash
pm2 start "bun run src/scripts/webhook-retry-cron.ts" --cron "*/5 * * * *" --name webhook-retry
```

## Security Considerations

1. **Always verify webhook signatures** - The system automatically validates all incoming webhooks
2. **Use HTTPS in production** - Stripe requires HTTPS for webhook endpoints
3. **Keep webhook secrets secure** - Store in environment variables, never in code
4. **Implement idempotency** - The system prevents duplicate processing
5. **Rate limiting** - Consider implementing rate limiting for the webhook endpoint

## Troubleshooting

### Common Issues

1. **Webhook signature verification fails**
   - Check that `STRIPE_WEBHOOK_SECRET` is correct
   - Ensure you're using the raw request body for verification

2. **Events not being processed**
   - Check server logs for error messages
   - Verify the webhook URL is accessible from the internet
   - Ensure the correct events are selected in Stripe dashboard

3. **Duplicate processing**
   - The system includes idempotency checks
   - Check database for duplicate webhook events

### Debugging

Enable detailed logging by checking the console output:
- `[WEBHOOK]` prefixed logs show webhook processing
- Processing times are logged for performance monitoring
- Error details and stack traces are preserved

### Testing

Test your webhook endpoint:

```bash
# Test webhook endpoint is accessible
curl -X POST https://your-domain.com/webhooks/stripe

# Should return 400 with "Missing signature" error
```

## Database Schema

The system uses these database tables:

### webhook_events
- `stripe_event_id` - Unique Stripe event ID
- `event_type` - Type of webhook event
- `processed` - Whether the event was successfully processed
- `retry_count` - Number of retry attempts
- `error_message` - Error details if processing failed

### payment_intents
- `stripe_payment_intent_id` - Stripe payment intent ID
- `status` - Payment status (PENDING, SUCCEEDED, FAILED, etc.)
- `payment_type` - LICENSE_PURCHASE or DEVICE_EXPANSION

### licenses
- Created automatically when LICENSE_PURCHASE payments succeed
- `status` set to ACTIVE when payment succeeds
- `license_key` generated using secure random algorithm

## Support

If you encounter issues:

1. Check the server logs for detailed error messages
2. Verify your Stripe webhook configuration
3. Test with Stripe CLI for local development
4. Review the webhook event logs in your database

For additional help, refer to the [Stripe Webhook Documentation](https://stripe.com/docs/webhooks).
