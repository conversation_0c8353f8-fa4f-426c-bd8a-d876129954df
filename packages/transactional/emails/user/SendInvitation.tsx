import {
	<PERSON>,
	<PERSON><PERSON>,
	Container,
	<PERSON>,
	<PERSON><PERSON>,
	<PERSON>r,
	Html,
	Preview,
	Tailwind,
	Text,
} from "@react-email/components";
import { Logo } from "../../components/Logo";

interface InvitationProps {
	inviter: string;
	inviteeEmail: string;
	teamName: string;
	acceptUrl: string;
}

const InvitationEmail = ({
	inviter,
	inviteeEmail,
	teamName,
	acceptUrl,
}: InvitationProps) => {
	const subject = `You're invited to join ${teamName} on SnapBack`;

	return (
		<Html>
			<Head />
			<Preview>{subject}</Preview>
			<Tailwind>
				<Body className="bg-white font-sans">
					<Container className="mx-auto my-10 max-w-[500px] rounded border border-gray-200 p-6">
						<Logo />
						<Heading className="mb-4 font-semibold text-xl">{subject}</Heading>
						<Text>
							<strong>{inviter}</strong> has invited you (
							<strong>{inviteeEmail}</strong>) to join their SnapBack team{" "}
							<strong>{teamName}</strong>.
						</Text>

						<Button
							href={acceptUrl}
							className="mt-4 rounded bg-blue-600 px-4 py-2 text-white"
						>
							Accept Invitation
						</Button>

						<Text className="mt-4 text-gray-700">
							If you don’t want to join, you can ignore this email.
						</Text>

						<Hr className="my-6 border-gray-200" />
						<Text className="text-gray-500 text-xs">
							This invitation will expire in 7 days.
						</Text>
					</Container>
				</Body>
			</Tailwind>
		</Html>
	);
};

InvitationEmail.PreviewProps = {
	inviter: "Jane Doe",
	inviteeEmail: "<EMAIL>",
	teamName: "SnapBack Team",
	acceptUrl: "https://yoursite.com/invite/accept/12345",
} as InvitationProps;

export default InvitationEmail;
