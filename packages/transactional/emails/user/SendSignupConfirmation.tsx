import {
	<PERSON>,
	<PERSON><PERSON>,
	Con<PERSON>er,
	<PERSON>,
	<PERSON><PERSON>,
	Hr,
	Html,
	Preview,
	Tail<PERSON>,
	Text,
} from "@react-email/components";
import { Logo } from "../../components/Logo";

interface RegistrationConfirmationProps {
	email: string;
	confirmUrl: string;
}

const RegistrationConfirmationEmail = ({
	email,
	confirmUrl,
}: RegistrationConfirmationProps) => {
	const subject = "Confirm Your SnapBack Account";

	return (
		<Html>
			<Head />
			<Preview>{subject}</Preview>
			<Tailwind>
				<Body className="bg-white font-sans">
					<Container className="mx-auto my-10 max-w-[500px] rounded border border-gray-200 p-6">
						<Logo />
						<Heading className="mb-4 font-semibold text-xl">{subject}</Heading>
						<Text>
							Thanks for signing up, <strong>{email}</strong>! Please confirm
							your account by clicking the button below:
						</Text>

						<Button
							href={confirmUrl}
							className="mt-4 rounded bg-blue-600 px-4 py-2 text-white"
						>
							Confirm Account
						</Button>

						<Text className="mt-4 text-gray-700">
							If you didn’t create a SnapBack account, you can safely ignore
							this email.
						</Text>

						<Hr className="my-6 border-gray-200" />
						<Text className="text-gray-500 text-xs">
							This link will expire in 24 hours.
						</Text>
					</Container>
				</Body>
			</Tailwind>
		</Html>
	);
};

RegistrationConfirmationEmail.PreviewProps = {
	email: "<EMAIL>",
	confirmUrl: "https://yoursite.com/confirm/abcd1234",
} as RegistrationConfirmationProps;

export default RegistrationConfirmationEmail;
