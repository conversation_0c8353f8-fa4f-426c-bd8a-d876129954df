import {
	Body,
	Container,
	Head,
	Heading,
	Hr,
	Html,
	Preview,
	Tailwind,
	Text,
} from "@react-email/components";
import { Logo } from "../../components/Logo";

interface RefundRequestProps {
	email: string;
	amount: string;
}

const RefundRequestConfirmationEmail = ({
	email,
	amount,
}: RefundRequestProps) => {
	const subject = "We Received Your SnapBack Refund Request";

	return (
		<Html>
			<Head />
			<Preview>{subject}</Preview>
			<Tailwind>
				<Body className="bg-white font-sans">
					<Container className="mx-auto my-10 max-w-[500px] rounded border border-gray-200 p-6">
						<Logo />
						<Heading className="mb-4 font-semibold text-xl">{subject}</Heading>
						<Text>
							We’ve received your refund request for <strong>{amount}</strong>.
							Our team will review it shortly.
						</Text>
						<Text>
							You’ll receive another email once your request has been approved
							or denied.
						</Text>
						<Hr className="my-6 border-gray-200" />
						<Text className="text-gray-500 text-xs">
							You don’t need to reply to this email.
						</Text>
					</Container>
				</Body>
			</Tailwind>
		</Html>
	);
};

RefundRequestConfirmationEmail.PreviewProps = {
	email: "<EMAIL>",
	amount: "$19.99",
} as RefundRequestProps;

export default RefundRequestConfirmationEmail;
