import {
	Body,
	Container,
	Head,
	Heading,
	Hr,
	Html,
	Preview,
	Tailwind,
	Text,
} from "@react-email/components";
import { LicenseKey } from "../../components/LicenseKey";
import { Logo } from "../../components/Logo";

interface TierUpgradeProps {
	email: string;
	oldTier: string;
	newTier: string;
	licenseKey: string;
}

const TierUpgradeEmail = ({
	email,
	oldTier,
	newTier,
	licenseKey,
}: TierUpgradeProps) => {
	const subject = "Your SnapBack License Has Been Upgraded";

	return (
		<Html>
			<Head />
			<Preview>{subject}</Preview>
			<Tailwind>
				<Body className="bg-white font-sans">
					<Container className="mx-auto my-10 max-w-[500px] rounded border border-gray-200 p-6">
						<Logo />
						<Heading className="mb-4 font-semibold text-xl">{subject}</Heading>
						<Text>
							Congratulations! Your SnapBack license has been upgraded from{" "}
							<strong>{oldTier.toUpperCase()}</strong> to{" "}
							<strong>{newTier.toUpperCase()}</strong>.
						</Text>
						<Text>
							Enjoy your new features and expanded device limits. You can
							continue using the same license key.
						</Text>
						<LicenseKey licenseKey={licenseKey} />
						<Text>
							<strong>License Type:</strong> {newTier.toUpperCase()}
						</Text>
						<Text>
							<strong>Devices Allowed:</strong> {newTier === "pro" ? "2" : "1"}
						</Text>
						<Hr className="my-6 border-gray-200" />
						<Text className="text-gray-500 text-xs">
							If you didn’t request this upgrade, please contact us immediately.
						</Text>
					</Container>
				</Body>
			</Tailwind>
		</Html>
	);
};

TierUpgradeEmail.PreviewProps = {
	email: "<EMAIL>",
	oldTier: "basic",
	newTier: "pro",
	licenseKey: "SNAP-1234-5678-KEY",
} as TierUpgradeProps;

export default TierUpgradeEmail;
