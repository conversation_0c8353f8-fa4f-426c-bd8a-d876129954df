import {
	<PERSON>,
	<PERSON><PERSON>,
	Container,
	Head,
	<PERSON>ing,
	Hr,
	Html,
	Img,
	Preview,
	Tailwind,
	Text,
} from "@react-email/components";
import { LicenseKey } from "../../components/LicenseKey";
import { Logo } from "../../components/Logo";

interface LicenseEmailProps {
	email: string;
	licenseKey: string;
	licenseType: string;
	expiresAt?: Date | null;
}

const baseUrl = process.env.NEXT_PUBLIC_APP_URL || "https://yourdomain.com";

const LicenseEmail = ({
	email,
	licenseKey,
	licenseType,
	expiresAt = null,
}: LicenseEmailProps) => {
	const isTrial = !!expiresAt;
	const subject = isTrial
		? "Your SnapBack Free Trial License Key"
		: "Your SnapBack License Key";

	const expirationText = isTrial
		? `This trial license expires on ${new Date(
				expiresAt,
			).toLocaleDateString()}. Download SnapBack and enter this license key to start your free trial!`
		: "This is a permanent license.";

	return (
		<Html>
			<Head />
			<Preview>{subject}</Preview>
			<Tailwind>
				<Body className="bg-white font-sans">
					<Container className="mx-auto my-10 max-w-[500px] rounded border border-gray-200 p-6">
						{/* Logo */}
						<Logo />

						<Heading className="mb-4 font-semibold text-xl">{subject}</Heading>

						<Text className="text-gray-900">
							{isTrial
								? "Thank you for trying SnapBack! Here's your free trial license:"
								: "Thank you for your purchase! Here's your license information:"}
						</Text>

						<LicenseKey licenseKey={licenseKey} />

						<Text>
							<strong>License Type:</strong> {licenseType.toUpperCase()}
						</Text>
						<Text>
							<strong>Devices Allowed:</strong>{" "}
							{licenseType === "pro" ? "2" : "1"}
						</Text>
						<Text>{expirationText}</Text>

						<Hr className="my-6 border-gray-200" />
						<Text className="text-gray-500 text-xs">
							If you lose this email, you can request your license key again
							using the same email address.
						</Text>
					</Container>
				</Body>
			</Tailwind>
		</Html>
	);
};

LicenseEmail.PreviewProps = {
	email: "<EMAIL>",
	licenseKey: "SNAP-1234-5678-KEY",
	licenseType: "trial",
	expiresAt: new Date(),
} as LicenseEmailProps;

export default LicenseEmail;
