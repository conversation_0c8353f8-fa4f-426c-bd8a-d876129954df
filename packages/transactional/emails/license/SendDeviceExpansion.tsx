import {
	Body,
	Container,
	Head,
	Heading,
	Hr,
	Html,
	Preview,
	Tailwind,
	Text,
} from "@react-email/components";
import { Footer } from "../../components/Footer";
import { LicenseKey } from "../../components/LicenseKey";
import { Logo } from "../../components/Logo";

interface DeviceExpansionProps {
	email: string;
	newLimit: number;
	licenseKey: string;
	licenseType: string;
}

const DeviceExpansionEmail = ({
	email,
	newLimit,
	licenseKey,
	licenseType,
}: DeviceExpansionProps) => {
	const subject = "Your SnapBack Device Limit Has Been Expanded";

	return (
		<Html>
			<Head />
			<Preview>{subject}</Preview>
			<Tailwind>
				<Body className="bg-white font-sans">
					<Container className="mx-auto my-10 max-w-[500px] rounded border border-gray-200 p-6">
						<Logo />
						<Heading className="mb-4 font-semibold text-xl">{subject}</Heading>
						<Text>
							Your SnapBack license now supports up to{" "}
							<strong>{newLimit} devices</strong>.
						</Text>
						<Text>
							You can activate SnapBack on your additional devices right away
							using your existing license key.
						</Text>
						<LicenseKey licenseKey={licenseKey} />
						<Text>
							<strong>License Type:</strong> {licenseType.toUpperCase()}
						</Text>
						<Text>
							<strong>Devices Allowed:</strong> {newLimit}
						</Text>
						<Hr className="my-6 border-gray-200" />
						<Text className="text-gray-500 text-xs">
							If you didn’t request this change, please contact us immediately.
						</Text>
					</Container>
				</Body>
			</Tailwind>
		</Html>
	);
};

DeviceExpansionEmail.PreviewProps = {
	email: "<EMAIL>",
	newLimit: 5,
	licenseKey: "SNAP-1234-5678-KEY",
	licenseType: "pro",
} as DeviceExpansionProps;

export default DeviceExpansionEmail;
