import {
	Body,
	Container,
	Head,
	Heading,
	Hr,
	Html,
	Preview,
	Tailwind,
	Text,
} from "@react-email/components";
import { LicenseKey } from "../../components/LicenseKey";
import { Logo } from "../../components/Logo";

interface LicenseRetrievalProps {
	email: string;
	licenseKey: string;
	licenseType: string;
}

const LicenseRetrievalEmail = ({
	email,
	licenseKey,
	licenseType,
}: LicenseRetrievalProps) => {
	const subject = "Your SnapBack License Retrieval";

	return (
		<Html>
			<Head />
			<Preview>{subject}</Preview>
			<Tailwind>
				<Body className="bg-white font-sans">
					<Container className="mx-auto my-10 max-w-[500px] rounded border border-gray-200 p-6">
						<Logo />
						<Heading className="mb-4 font-semibold text-xl">{subject}</Heading>
						<Text>
							Here’s the SnapBack license key we found associated with{" "}
							<strong>{email}</strong>:
						</Text>

						<LicenseKey licenseKey={licenseKey} />

						<Text>
							<strong>License Type:</strong> {licenseType.toUpperCase()}
						</Text>
						<Text>
							<strong>Devices Allowed:</strong>{" "}
							{licenseType === "pro" ? "2" : "1"}
						</Text>

						<Hr className="my-6 border-gray-200" />
						<Text className="text-gray-500 text-xs">
							If you didn’t request this, you can safely ignore this email.
						</Text>
					</Container>
				</Body>
			</Tailwind>
		</Html>
	);
};

LicenseRetrievalEmail.PreviewProps = {
	email: "<EMAIL>",
	licenseKey: "SNAP-LOST-1234",
	licenseType: "pro",
} as LicenseRetrievalProps;

export default LicenseRetrievalEmail;
