import {
	Body,
	Container,
	Head,
	Heading,
	Hr,
	Html,
	Preview,
	Tailwind,
	Text,
} from "@react-email/components";
import { Logo } from "../../components/Logo";

interface RefundRejectionProps {
	email: string;
	reason: string;
}

const RefundRejectionEmail = ({ email, reason }: RefundRejectionProps) => {
	const subject = "Your SnapBack Refund Request Has Been Denied";

	return (
		<Html>
			<Head />
			<Preview>{subject}</Preview>
			<Tailwind>
				<Body className="bg-white font-sans">
					<Container className="mx-auto my-10 max-w-[500px] rounded border border-gray-200 p-6">
						<Logo />
						<Heading className="mb-4 font-semibold text-xl">{subject}</Heading>
						<Text>
							Unfortunately, we were unable to approve your refund request.
						</Text>
						<Text>
							<strong>Reason:</strong> {reason}
						</Text>
						<Text>
							If you believe this decision was made in error, please reply to
							this email with additional information.
						</Text>
						<Hr className="my-6 border-gray-200" />
						<Text className="text-gray-500 text-xs">
							Thank you for understanding.
						</Text>
					</Container>
				</Body>
			</Tailwind>
		</Html>
	);
};

RefundRejectionEmail.PreviewProps = {
	email: "<EMAIL>",
	reason: "Your purchase was made more than 30 days ago.",
} as RefundRejectionProps;

export default RefundRejectionEmail;
