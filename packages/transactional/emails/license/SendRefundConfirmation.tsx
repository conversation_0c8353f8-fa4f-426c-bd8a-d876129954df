import {
	Body,
	Container,
	Head,
	Heading,
	Hr,
	Html,
	Preview,
	Tailwind,
	Text,
} from "@react-email/components";
import { Logo } from "../../components/Logo";

interface RefundConfirmationProps {
	email: string;
	amount: string;
}

const RefundConfirmationEmail = ({
	email,
	amount,
}: RefundConfirmationProps) => {
	const subject = "Your SnapBack Refund Has Been Processed";

	return (
		<Html>
			<Head />
			<Preview>{subject}</Preview>
			<Tailwind>
				<Body className="bg-white font-sans">
					<Container className="mx-auto my-10 max-w-[500px] rounded border border-gray-200 p-6">
						<Logo />
						<Heading className="mb-4 font-semibold text-xl">{subject}</Heading>
						<Text>
							We’ve processed your refund of <strong>{amount}</strong> for your
							SnapBack license purchase.
						</Text>
						<Text>
							The refund should appear on your original payment method within
							5–10 business days.
						</Text>
						<Hr className="my-6 border-gray-200" />
						<Text className="text-gray-500 text-xs">
							If you have any questions, reply to this email.
						</Text>
					</Container>
				</Body>
			</Tailwind>
		</Html>
	);
};

RefundConfirmationEmail.PreviewProps = {
	email: "<EMAIL>",
	amount: "$19.99",
} as RefundConfirmationProps;

export default RefundConfirmationEmail;
