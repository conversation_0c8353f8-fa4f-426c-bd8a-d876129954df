{"name": "snapback-license-system", "private": true, "type": "module", "workspaces": ["apps/*", "packages/*"], "scripts": {"check": "biome check --write .", "prepare": "husky", "dev": "turbo dev", "build": "turbo build", "check-types": "turbo check-types", "dev:native": "turbo -F native dev", "dev:web": "turbo -F web dev", "dev:server": "turbo -F server dev", "db:push": "turbo run db:push --filter=@repo/db", "db:studio": "turbo run db:studio --filter=@repo/db", "db:generate": "turbo run db:generate --filter=@repo/db", "db:migrate": "turbo run db:migrate --filter=@repo/db", "db:reset": "turbo run db:reset --filter=@repo/db", "db:start": "turbo -F server dev", "db:watch": "turbo -F server dev", "db:stop": "turbo  -F server dev", "db:down": "turbos -F server dev", "db:seed": "turbo run db:seed --filter=@repo/db", "stripe:listen": "turbo -F server stripe:listen"}, "dependencies": {"react-hook-form": "^7.62.0"}, "devDependencies": {"turbo": "^2.5.4", "@biomejs/biome": "^2.2.0", "husky": "^9.1.7", "lint-staged": "^16.1.2"}, "lint-staged": {"*.{js,ts,cjs,mjs,d.cts,d.mts,jsx,tsx,json,jsonc}": ["biome check --write ."]}, "packageManager": "bun@1.2.21"}