{"name": "native", "version": "1.0.0", "main": "expo-router/entry", "scripts": {"dev": "expo start --clear", "android": "expo run:android", "ios": "expo run:ios", "prebuild": "expo prebuild", "web": "expo start --web"}, "dependencies": {"@expo/vector-icons": "^15.0.2", "@react-navigation/bottom-tabs": "^7.2.0", "@react-navigation/drawer": "^7.1.1", "@react-navigation/native": "^7.0.14", "@tanstack/react-form": "^1.0.5", "@tanstack/react-query": "^5.85.5", "expo": "^54.0.1", "expo-constants": "~18.0.8", "expo-crypto": "~15.0.6", "expo-linking": "~8.0.7", "expo-navigation-bar": "~5.0.8", "expo-router": "~6.0.0", "expo-secure-store": "~15.0.6", "expo-splash-screen": "~31.0.8", "expo-status-bar": "~3.0.7", "expo-system-ui": "~6.0.7", "expo-web-browser": "~15.0.6", "nativewind": "^4.1.23", "react": "19.1.0", "react-dom": "19.1.0", "react-native": "0.81.4", "react-native-gesture-handler": "~2.28.0", "react-native-reanimated": "~4.1.0", "react-native-safe-area-context": "~5.6.0", "react-native-screens": "~4.16.0", "react-native-web": "^0.21.0", "react-native-worklets": "^0.5.1", "@orpc/tanstack-query": "^1.8.6", "@orpc/client": "^1.8.6", "better-auth": "^1.3.9", "@better-auth/expo": "^1.3.9"}, "devDependencies": {"@babel/core": "^7.26.10", "@types/react": "~19.1.10", "tailwindcss": "^3.4.17", "typescript": "~5.8.2", "@repo/db": "workspace:*"}, "private": true}