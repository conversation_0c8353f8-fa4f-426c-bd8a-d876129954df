module.exports = {
	preset: "bun",
	testEnvironment: "node",
	roots: ["<rootDir>/src"],
	testMatch: [
		"**/__tests__/**/*.+(ts|tsx|js)",
		"**/*.(test|spec).+(ts|tsx|js)",
	],
	transform: {
		"^.+\\.(ts|tsx)$": "bun",
	},
	collectCoverageFrom: [
		"src/**/*.{ts,tsx}",
		"!src/**/*.d.ts",
		"!src/index.ts",
		"!src/scripts/**/*",
	],
	setupFilesAfterEnv: ["<rootDir>/src/__tests__/setup.ts"],
	moduleNameMapping: {
		"^@/(.*)$": "<rootDir>/src/$1",
	},
	clearMocks: true,
	resetMocks: true,
	restoreMocks: true,
};
