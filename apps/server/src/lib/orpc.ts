import { ORPCError, os } from "@orpc/server";
import type { UserRole } from "@repo/db";
import type { Context } from "./context";
import { prismaErrorMiddleware } from "./prisma-error-middleware";

export const o = os.$context<Context>();

// Apply Prisma error handling middleware globally to all procedures
export const publicProcedure = o.use(prismaErrorMiddleware);

const requireAuth = o.middleware(async ({ context, next }) => {
	if (!context.session?.user) {
		throw new ORPCError("UNAUTHORIZED");
	}
	return next({
		context: {
			session: context.session,
		},
	});
});

export const protectedProcedure = publicProcedure.use(requireAuth);

const requireRole = (roles: string[]) => {
	return o.middleware(async ({ context, next }) => {
		if (!context.session?.user) {
			throw new ORPCError("UNAUTHORIZED");
		}
		if (!roles.includes(context.session.user.role as UserRole)) {
			throw new ORPCError("FORBIDDEN");
		}
		return next({
			context: {
				session: context.session,
			},
		});
	});
};

export const collaboratorProcedure = protectedProcedure.use(
	requireRole(["COLLABORATOR", "ADMIN", "SUPER_ADMIN"]),
);
export const adminProcedure = protectedProcedure.use(
	requireRole(["ADMIN", "SUPER_ADMIN"]),
);
export const superAdminProcedure = protectedProcedure.use(
	requireRole(["SUPER_ADMIN"]),
);
