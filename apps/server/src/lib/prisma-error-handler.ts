import { ORPCError } from "@orpc/server";
import { Prisma } from "@repo/db";

/**
 * Prisma error code mappings to ORPC error types
 * Based on Prisma error reference: https://www.prisma.io/docs/orm/reference/error-reference
 */
const PRISMA_ERROR_MAPPINGS = {
	// Unique constraint violations
	P2002: {
		orpcCode: "CONFLICT" as const,
		getMessage: (error: Prisma.PrismaClientKnownRequestError) => {
			const target = error.meta?.target as string[] | undefined;
			const fields = target ? target.join(", ") : "field";
			return `A record with this ${fields} already exists`;
		},
	},
	// Record not found
	P2025: {
		orpcCode: "NOT_FOUND" as const,
		getMessage: (error: Prisma.PrismaClientKnownRequestError) => {
			return error.message || "The requested record was not found";
		},
	},
	// Foreign key constraint violation
	P2003: {
		orpcCode: "BAD_REQUEST" as const,
		// biome-ignore lint/correctness/noUnusedFunctionParameters: used for type safety
		getMessage: (error: Prisma.PrismaClientKnownRequestError) => {
			return "Invalid reference to related record";
		},
	},
	// Database constraint violation
	P2004: {
		orpcCode: "BAD_REQUEST" as const,
		// biome-ignore lint/correctness/noUnusedFunctionParameters: used for type safety
		getMessage: (error: Prisma.PrismaClientKnownRequestError) => {
			return "Database constraint violation";
		},
	},
	// Value stored in database is invalid for field type
	P2005: {
		orpcCode: "BAD_REQUEST" as const,
		// biome-ignore lint/correctness/noUnusedFunctionParameters: used for type safety
		getMessage: (error: Prisma.PrismaClientKnownRequestError) => {
			return "Invalid value for field type";
		},
	},
	// Value provided for field is not valid
	P2006: {
		orpcCode: "BAD_REQUEST" as const,
		// biome-ignore lint/correctness/noUnusedFunctionParameters: used for type safety
		getMessage: (error: Prisma.PrismaClientKnownRequestError) => {
			return "Invalid value provided for field";
		},
	},
	// Data validation error
	P2007: {
		orpcCode: "BAD_REQUEST" as const,
		// biome-ignore lint/correctness/noUnusedFunctionParameters: used for type safety
		getMessage: (error: Prisma.PrismaClientKnownRequestError) => {
			return "Data validation error";
		},
	},
	// Failed to parse query
	P2008: {
		orpcCode: "BAD_REQUEST" as const,
		// biome-ignore lint/correctness/noUnusedFunctionParameters: used for type safety
		getMessage: (error: Prisma.PrismaClientKnownRequestError) => {
			return "Failed to parse query";
		},
	},
	// Failed to validate query
	P2009: {
		orpcCode: "BAD_REQUEST" as const,
		// biome-ignore lint/correctness/noUnusedFunctionParameters: used for type safety
		getMessage: (error: Prisma.PrismaClientKnownRequestError) => {
			return "Failed to validate query";
		},
	},
	// Raw query failed
	P2010: {
		orpcCode: "INTERNAL_SERVER_ERROR" as const,
		// biome-ignore lint/correctness/noUnusedFunctionParameters: used for type safety
		getMessage: (error: Prisma.PrismaClientKnownRequestError) => {
			return "Database query failed";
		},
	},
	// Null constraint violation
	P2011: {
		orpcCode: "BAD_REQUEST" as const,
		getMessage: (error: Prisma.PrismaClientKnownRequestError) => {
			const target = error.meta?.target as string | undefined;
			return `Required field ${target || "field"} cannot be null`;
		},
	},
	// Missing required value
	P2012: {
		orpcCode: "BAD_REQUEST" as const,
		// biome-ignore lint/correctness/noUnusedFunctionParameters: used for type safety
		getMessage: (error: Prisma.PrismaClientKnownRequestError) => {
			return "Missing required value";
		},
	},
	// Missing required argument
	P2013: {
		orpcCode: "BAD_REQUEST" as const,
		// biome-ignore lint/correctness/noUnusedFunctionParameters: used for type safety
		getMessage: (error: Prisma.PrismaClientKnownRequestError) => {
			return "Missing required argument";
		},
	},
	// Required relation is missing
	P2014: {
		orpcCode: "BAD_REQUEST" as const,
		// biome-ignore lint/correctness/noUnusedFunctionParameters: used for type safety
		getMessage: (error: Prisma.PrismaClientKnownRequestError) => {
			return "Required relation is missing";
		},
	},
	// Related record not found
	P2015: {
		orpcCode: "NOT_FOUND" as const,
		// biome-ignore lint/correctness/noUnusedFunctionParameters: used for type safety
		getMessage: (error: Prisma.PrismaClientKnownRequestError) => {
			return "Related record not found";
		},
	},
	// Query interpretation error
	P2016: {
		orpcCode: "BAD_REQUEST" as const,
		// biome-ignore lint/correctness/noUnusedFunctionParameters: used for type safety
		getMessage: (error: Prisma.PrismaClientKnownRequestError) => {
			return "Query interpretation error";
		},
	},
	// Records for relation are not connected
	P2017: {
		orpcCode: "BAD_REQUEST" as const,
		// biome-ignore lint/correctness/noUnusedFunctionParameters: used for type safety
		getMessage: (error: Prisma.PrismaClientKnownRequestError) => {
			return "Records for relation are not connected";
		},
	},
	// Required connected records not found
	P2018: {
		orpcCode: "NOT_FOUND" as const,
		// biome-ignore lint/correctness/noUnusedFunctionParameters: used for type safety
		getMessage: (error: Prisma.PrismaClientKnownRequestError) => {
			return "Required connected records not found";
		},
	},
	// Input error
	P2019: {
		orpcCode: "BAD_REQUEST" as const,
		// biome-ignore lint/correctness/noUnusedFunctionParameters: used for type safety
		getMessage: (error: Prisma.PrismaClientKnownRequestError) => {
			return "Input error";
		},
	},
	// Value out of range
	P2020: {
		orpcCode: "BAD_REQUEST" as const,
		// biome-ignore lint/correctness/noUnusedFunctionParameters: used for type safety
		getMessage: (error: Prisma.PrismaClientKnownRequestError) => {
			return "Value out of range";
		},
	},
	// Table does not exist
	P2021: {
		orpcCode: "INTERNAL_SERVER_ERROR" as const,
		// biome-ignore lint/correctness/noUnusedFunctionParameters: used for type safety
		getMessage: (error: Prisma.PrismaClientKnownRequestError) => {
			return "Database table does not exist";
		},
	},
	// Column does not exist
	P2022: {
		orpcCode: "INTERNAL_SERVER_ERROR" as const,
		// biome-ignore lint/correctness/noUnusedFunctionParameters: used for type safety
		getMessage: (error: Prisma.PrismaClientKnownRequestError) => {
			return "Database column does not exist";
		},
	},
	// Inconsistent column data
	P2023: {
		orpcCode: "INTERNAL_SERVER_ERROR" as const,
		// biome-ignore lint/correctness/noUnusedFunctionParameters: used for type safety
		getMessage: (error: Prisma.PrismaClientKnownRequestError) => {
			return "Inconsistent column data";
		},
	},
	// Connection timeout
	P2024: {
		orpcCode: "INTERNAL_SERVER_ERROR" as const,
		// biome-ignore lint/correctness/noUnusedFunctionParameters: used for type safety
		getMessage: (error: Prisma.PrismaClientKnownRequestError) => {
			return "Database connection timeout";
		},
	},
} as const;

/**
 * Interface for error conversion result
 */
interface ErrorConversionResult {
	// biome-ignore lint/suspicious/noExplicitAny: don't need to be this specific
	orpcError: ORPCError<any, any>;
	originalError: Error;
}

/**
 * Converts Prisma errors to appropriate ORPC errors
 * @param error - The error to convert
 * @returns ErrorConversionResult if it's a convertible Prisma error, null otherwise
 */
export function convertPrismaError(
	error: unknown,
): ErrorConversionResult | null {
	// Handle Prisma Client Known Request Errors (P-codes)
	if (error instanceof Prisma.PrismaClientKnownRequestError) {
		const mapping =
			PRISMA_ERROR_MAPPINGS[error.code as keyof typeof PRISMA_ERROR_MAPPINGS];

		if (mapping) {
			const message = mapping.getMessage(error);
			const orpcError = new ORPCError(mapping.orpcCode, {
				message,
				data: {
					prismaCode: error.code,
					prismaMessage: error.message,
					prismaMeta: error.meta,
					// Include original error details for debugging (server-side only)
					...(process.env.NODE_ENV === "development" && {
						originalStack: error.stack,
					}),
				},
			});

			return {
				orpcError,
				originalError: error,
			};
		}
	}

	// Handle Prisma Client Validation Errors
	if (error instanceof Prisma.PrismaClientValidationError) {
		const orpcError = new ORPCError("BAD_REQUEST", {
			message: "Invalid data provided",
			data: {
				prismaMessage: error.message,
				...(process.env.NODE_ENV === "development" && {
					originalStack: error.stack,
				}),
			},
		});

		return {
			orpcError,
			originalError: error,
		};
	}

	// Handle Prisma Client Initialization Errors
	if (error instanceof Prisma.PrismaClientInitializationError) {
		const orpcError = new ORPCError("INTERNAL_SERVER_ERROR", {
			message: "Database connection error",
			data: {
				prismaMessage: "Database initialization failed",
				...(process.env.NODE_ENV === "development" && {
					originalStack: error.stack,
					originalMessage: error.message,
				}),
			},
		});

		return {
			orpcError,
			originalError: error,
		};
	}

	// Handle Prisma Client Rust Panic Errors
	if (error instanceof Prisma.PrismaClientRustPanicError) {
		const orpcError = new ORPCError("INTERNAL_SERVER_ERROR", {
			message: "Database engine error",
			data: {
				prismaMessage: "Database engine panic",
				...(process.env.NODE_ENV === "development" && {
					originalStack: error.stack,
					originalMessage: error.message,
				}),
			},
		});

		return {
			orpcError,
			originalError: error,
		};
	}

	// Handle Prisma Client Unknown Request Errors
	if (error instanceof Prisma.PrismaClientUnknownRequestError) {
		const orpcError = new ORPCError("INTERNAL_SERVER_ERROR", {
			message: "Unknown database error",
			data: {
				prismaMessage: error.message,
				...(process.env.NODE_ENV === "development" && {
					originalStack: error.stack,
				}),
			},
		});

		return {
			orpcError,
			originalError: error,
		};
	}

	// Not a Prisma error we can handle
	return null;
}

/**
 * Type guard to check if an error is a Prisma error
 * @param error - The error to check
 * @returns true if it's a Prisma error
 */
export function isPrismaError(
	error: unknown,
): error is
	| Prisma.PrismaClientKnownRequestError
	| Prisma.PrismaClientValidationError
	| Prisma.PrismaClientInitializationError
	| Prisma.PrismaClientRustPanicError
	| Prisma.PrismaClientUnknownRequestError {
	return (
		error instanceof Prisma.PrismaClientKnownRequestError ||
		error instanceof Prisma.PrismaClientValidationError ||
		error instanceof Prisma.PrismaClientInitializationError ||
		error instanceof Prisma.PrismaClientRustPanicError ||
		error instanceof Prisma.PrismaClientUnknownRequestError
	);
}
