import { prisma } from "@repo/db";
import type <PERSON><PERSON> from "stripe";
import {
	activateLicensesForPayment,
	createWebhookAuditLog,
	getPaymentIntentByStripeId,
	markEventProcessed,
	processDeviceExpansion,
	recordWebhookEvent,
	updatePaymentIntentStatus,
} from "@/lib/webhook";

/**
 * Main webhook event processor
 * Handles different types of Stripe webhook events
 */
export class WebhookProcessor {
	/**
	 * Process a Stripe webhook event
	 * @param event - Verified Stripe event object
	 */
	async processEvent(event: Stripe.Event): Promise<void> {
		const startTime = Date.now();
		console.log(`[WEBHOOK] Processing event: ${event.type} (${event.id})`);

		try {
			// Record the event in database
			let paymentIntentId: string | undefined;

			// Extract payment intent ID if available
			if (this.isPaymentIntentEvent(event)) {
				const paymentIntent = event.data.object as Stripe.PaymentIntent;
				const dbPaymentIntent = await getPaymentIntentByStripeId(
					paymentIntent.id,
				);
				paymentIntentId = dbPaymentIntent?.id;
			} else if (this.isCheckoutSessionEvent(event)) {
				const session = event.data.object as Stripe.Checkout.Session;
				if (
					session.payment_intent &&
					typeof session.payment_intent === "string"
				) {
					const dbPaymentIntent = await getPaymentIntentByStripeId(
						session.payment_intent,
					);
					paymentIntentId = dbPaymentIntent?.id;
				}
			}

			await recordWebhookEvent(event, paymentIntentId);

			// Process the event based on its type
			switch (event.type) {
				case "payment_intent.succeeded":
					await this.handlePaymentIntentSucceeded(event);
					break;
				case "payment_intent.payment_failed":
					await this.handlePaymentIntentFailed(event);
					break;
				case "checkout.session.completed":
					await this.handleCheckoutSessionCompleted(event);
					break;
				case "payment_intent.canceled":
					await this.handlePaymentIntentCanceled(event);
					break;
				case "invoice.payment_succeeded":
					await this.handleInvoicePaymentSucceeded(event);
					break;
				case "invoice.payment_failed":
					await this.handleInvoicePaymentFailed(event);
					break;
				default:
					console.log(`Unhandled webhook event type: ${event.type}`);
					await this.handleUnknownEvent(event);
			}

			// Mark event as successfully processed
			await markEventProcessed(event.id, true);
			const processingTime = Date.now() - startTime;
			console.log(
				`[WEBHOOK] Successfully processed event ${event.id} in ${processingTime}ms`,
			);
		} catch (error) {
			const processingTime = Date.now() - startTime;
			console.error(
				`[WEBHOOK] Error processing event ${event.id} after ${processingTime}ms:`,
				error,
			);

			// Enhanced error logging
			if (error instanceof Error) {
				console.error(`[WEBHOOK] Error details: ${error.message}`);
				console.error(`[WEBHOOK] Stack trace: ${error.stack}`);
			}

			await markEventProcessed(
				event.id,
				false,
				error instanceof Error ? error.message : String(error),
			);
			throw error;
		}
	}

	/**
	 * Handle payment_intent.succeeded event
	 * Activates licenses and processes device expansions
	 */
	private async handlePaymentIntentSucceeded(
		event: Stripe.Event,
	): Promise<void> {
		const paymentIntent = event.data.object as Stripe.PaymentIntent;
		console.log(`Payment succeeded: ${paymentIntent.id}`);

		const dbPaymentIntent = await getPaymentIntentByStripeId(paymentIntent.id);
		if (!dbPaymentIntent) {
			throw new Error(
				`Payment intent not found in database: ${paymentIntent.id}`,
			);
		}

		// Update payment status
		await updatePaymentIntentStatus(paymentIntent.id, "SUCCEEDED", new Date());

		// Activate licenses for license purchases
		if (dbPaymentIntent.paymentType === "LICENSE_PURCHASE") {
			await activateLicensesForPayment(dbPaymentIntent.id);
			console.log(`Activated licenses for payment: ${paymentIntent.id}`);
		}

		// Process device expansions
		if (dbPaymentIntent.paymentType === "DEVICE_EXPANSION") {
			await processDeviceExpansion(dbPaymentIntent.id);
			console.log(
				`Processed device expansion for payment: ${paymentIntent.id}`,
			);
		}

		// Create audit log
		await createWebhookAuditLog(
			"PAYMENT_SUCCEEDED",
			{
				stripePaymentIntentId: paymentIntent.id,
				amount: paymentIntent.amount,
				currency: paymentIntent.currency,
				paymentType: dbPaymentIntent.paymentType,
			},
			dbPaymentIntent.licenses[0]?.id,
			dbPaymentIntent.customerEmail,
		);
	}

	/**
	 * Handle payment_intent.payment_failed event
	 * Marks payment as failed and logs the failure
	 */
	private async handlePaymentIntentFailed(event: Stripe.Event): Promise<void> {
		const paymentIntent = event.data.object as Stripe.PaymentIntent;
		console.log(`Payment failed: ${paymentIntent.id}`);

		const dbPaymentIntent = await getPaymentIntentByStripeId(paymentIntent.id);
		if (!dbPaymentIntent) {
			console.warn(`Payment intent not found in database: ${paymentIntent.id}`);
			return;
		}

		// Update payment status
		await updatePaymentIntentStatus(paymentIntent.id, "FAILED");

		// Create audit log
		await createWebhookAuditLog(
			"PAYMENT_FAILED",
			{
				stripePaymentIntentId: paymentIntent.id,
				amount: paymentIntent.amount,
				currency: paymentIntent.currency,
				lastPaymentError: paymentIntent.last_payment_error?.message || null,
			},
			dbPaymentIntent.licenses[0]?.id,
			dbPaymentIntent.customerEmail,
		);
	}

	/**
	 * Handle checkout.session.completed event
	 * Processes successful checkout sessions
	 */
	private async handleCheckoutSessionCompleted(
		event: Stripe.Event,
	): Promise<void> {
		const session = event.data.object as Stripe.Checkout.Session;
		console.log(`Checkout session completed: ${session.id}`);

		if (session.payment_intent && typeof session.payment_intent === "string") {
			const dbPaymentIntent = await getPaymentIntentByStripeId(
				session.payment_intent,
			);
			if (dbPaymentIntent) {
				// Update checkout session ID if not already set
				if (!dbPaymentIntent.stripeCheckoutSessionId) {
					await prisma.paymentIntent.update({
						where: { id: dbPaymentIntent.id },
						data: { stripeCheckoutSessionId: session.id },
					});
				}

				// Create audit log
				await createWebhookAuditLog(
					"WEBHOOK_PROCESSED",
					{
						eventType: "checkout.session.completed",
						sessionId: session.id,
						paymentIntentId: session.payment_intent,
						customerEmail: session.customer_details?.email || null,
					},
					dbPaymentIntent.licenses[0]?.id,
					session.customer_details?.email || dbPaymentIntent.customerEmail,
				);
			}
		}
	}

	/**
	 * Handle payment_intent.canceled event
	 */
	private async handlePaymentIntentCanceled(
		event: Stripe.Event,
	): Promise<void> {
		const paymentIntent = event.data.object as Stripe.PaymentIntent;
		console.log(`Payment canceled: ${paymentIntent.id}`);

		const dbPaymentIntent = await getPaymentIntentByStripeId(paymentIntent.id);
		if (dbPaymentIntent) {
			await updatePaymentIntentStatus(paymentIntent.id, "CANCELLED");
		}
	}

	/**
	 * Handle invoice.payment_succeeded event (for future subscription support)
	 */
	private async handleInvoicePaymentSucceeded(
		event: Stripe.Event,
	): Promise<void> {
		const invoice = event.data.object as Stripe.Invoice;
		console.log(`Invoice payment succeeded: ${invoice.id}`);
		// Future implementation for subscription handling
	}

	/**
	 * Handle invoice.payment_failed event (for future subscription support)
	 */
	private async handleInvoicePaymentFailed(event: Stripe.Event): Promise<void> {
		const invoice = event.data.object as Stripe.Invoice;
		console.log(`Invoice payment failed: ${invoice.id}`);
		// Future implementation for subscription handling
	}

	/**
	 * Handle unknown/unprocessed event types
	 */
	private async handleUnknownEvent(event: Stripe.Event): Promise<void> {
		console.log(`Received unhandled event type: ${event.type}`);
		// Just log for now, but mark as processed since we don't need to retry
	}

	/**
	 * Check if event is related to payment intent
	 */
	private isPaymentIntentEvent(event: Stripe.Event): boolean {
		return event.type.startsWith("payment_intent.");
	}

	/**
	 * Check if event is related to checkout session
	 */
	private isCheckoutSessionEvent(event: Stripe.Event): boolean {
		return event.type.startsWith("checkout.session.");
	}
}

// Export singleton instance
export const webhookProcessor = new WebhookProcessor();
