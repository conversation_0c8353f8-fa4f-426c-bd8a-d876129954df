import {
	CreateDeviceInputSchema,
	DevicePaginatedOutputSchema,
	IdInputSchema,
	PaginateInputSchema,
	prisma,
	UpdateDeviceInputSchema,
} from "@repo/db";

import { protectedProcedure } from "@/lib/orpc";
import { buildPaginationResponse } from "@/lib/pagination";

export const devices = {
	list: protectedProcedure
		.route({
			method: "GET",
			path: "/devices",
			summary: "List devices",
			description: "List devices",
			tags: ["devices"],
		})
		.handler(async ({ context }) => {
			return await prisma.device.findMany();
		}),
	paginate: protectedProcedure
		.route({
			method: "GET",
			path: "/devices/paginate",
			summary: "Paginated devices",
			description: "Paginated devices",
			tags: ["devices"],
		})
		.input(PaginateInputSchema)
		.output(DevicePaginatedOutputSchema)
		.handler(async ({ input }) => {
			return await buildPaginationResponse(prisma.device, input);
		}),

	get: protectedProcedure
		.route({
			method: "GET",
			path: "/devices/{id}",
			summary: "Get device",
			description: "Get device",
			tags: ["devices"],
		})
		.input(IdInputSchema)
		.handler(async ({ input }) => {
			return await prisma.device.findUnique({
				where: {
					id: input.id,
				},
			});
		}),

	create: protectedProcedure
		.route({
			method: "POST",
			path: "/devices",
			summary: "Create device",
			description: "Create device",
			tags: ["devices"],
		})
		.input(CreateDeviceInputSchema)
		.handler(async ({ input }) => {
			// TODO: Hash device ID
			const salt = crypto.randomUUID();
			const deviceHash = crypto.randomUUID();
			return await prisma.device.create({
				data: {
					licenseId: input.licenseId,
					appVersion: input.appVersion,
					deviceHash,
					salt,
					deviceName: input.deviceName,
					deviceType: input.deviceType,
					deviceModel: input.deviceModel,
					operatingSystem: input.operatingSystem,
					architecture: input.architecture,
					screenResolution: input.screenResolution,
					totalMemory: input.totalMemory,
					userNickname: input.userNickname,
					location: input.location,
					notes: input.notes,
				},
			});
		}),

	update: protectedProcedure
		.route({
			method: "PUT",
			path: "/devices",
			summary: "Update device",
			description: "Update device",
			tags: ["devices"],
		})
		.input(UpdateDeviceInputSchema)
		.handler(async ({ input }) => {
			return await prisma.device.update({
				where: {
					id: input.id,
				},
				data: {
					deviceName: input.deviceName,
					deviceType: input.deviceType,
					deviceModel: input.deviceModel,
					operatingSystem: input.operatingSystem,
					architecture: input.architecture,
					screenResolution: input.screenResolution,
					totalMemory: input.totalMemory,
					userNickname: input.userNickname,
					location: input.location,
					notes: input.notes,
				},
			});
		}),
	delete: protectedProcedure
		.route({
			method: "DELETE",
			path: "/devices/{id}",
			summary: "Delete device",
			description: "Delete device",
			tags: ["devices"],
		})
		.input(IdInputSchema)
		.handler(async ({ input }) => {
			return await prisma.device.delete({
				where: {
					id: input.id,
				},
			});
		}),
};
