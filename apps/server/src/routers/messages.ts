import {
	CreateMessageInputSchema,
	IdInputSchema,
	MessagePaginatedOutputSchema,
	PaginateInputSchema,
	prisma,
} from "@repo/db";

import { protectedProcedure } from "@/lib/orpc";
import { buildPaginationResponse } from "@/lib/pagination";

export const messages = {
	list: protectedProcedure
		.route({
			method: "GET",
			path: "/messages",
			summary: "List messages",
			description: "List messages",
			tags: ["messages"],
		})
		.handler(async ({ context }) => {
			return await prisma.supportMessage.findMany();
		}),
	paginate: protectedProcedure
		.route({
			method: "GET",
			path: "/messages/paginate",
			summary: "Paginated messages",
			description: "Paginated messages",
			tags: ["messages"],
		})
		.input(PaginateInputSchema)
		.output(MessagePaginatedOutputSchema)
		.handler(async ({ input }) => {
			return await buildPaginationResponse(prisma.supportMessage, input);
		}),

	get: protectedProcedure
		.route({
			method: "GET",
			path: "/messages/{id}",
			summary: "Get message",
			description: "Get message",
			tags: ["messages"],
		})
		.input(IdInputSchema)
		.handler(async ({ input }) => {
			return await prisma.supportMessage.findUnique({
				where: {
					id: input.id,
				},
			});
		}),
	create: protectedProcedure
		.route({
			method: "POST",
			path: "/messages",
			summary: "Create message",
			description: "Create message",
			tags: ["messages"],
		})
		.input(CreateMessageInputSchema)
		.handler(async ({ input }) => {
			return await prisma.supportMessage.create({
				data: {
					ticketId: input.ticketId,
					message: input.message,
					isInternal: input.isInternal,
					authorEmail: input.authorEmail,
					authorId: input.authorId,
				},
			});
		}),
	delete: protectedProcedure
		.route({
			method: "DELETE",
			path: "/messages/{id}",
			summary: "Delete message",
			description: "Delete message",
			tags: ["messages"],
		})
		.input(IdInputSchema)
		.handler(async ({ input }) => {
			return await prisma.supportMessage.delete({
				where: {
					id: input.id,
				},
			});
		}),
};
