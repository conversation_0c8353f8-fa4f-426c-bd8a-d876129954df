import {
	CreateTicketInputSchema,
	IdInputSchema,
	PaginateInputSchema,
	prisma,
	TicketPaginatedOutputSchema,
	UpdateTicketInputSchema,
} from "@repo/db";
import { protectedProcedure } from "@/lib/orpc";
import { buildPaginationResponse } from "@/lib/pagination";

export const tickets = {
	list: protectedProcedure
		.route({
			method: "GET",
			path: "/tickets",
			summary: "List tickets",
			description: "List tickets",
			tags: ["tickets"],
		})
		.handler(async ({ context }) => {
			return await prisma.supportTicket.findMany();
		}),
	paginate: protectedProcedure
		.route({
			method: "GET",
			path: "/tickets/paginate",
			summary: "Paginated tickets",
			description: "Paginated tickets",
			tags: ["tickets"],
		})
		.input(PaginateInputSchema)
		.output(TicketPaginatedOutputSchema)
		.handler(async ({ input }) => {
			return await buildPaginationResponse(prisma.supportTicket, input);
		}),

	get: protectedProcedure
		.route({
			method: "GET",
			path: "/tickets/{id}",
			summary: "Get ticket",
			description: "Get ticket",
			tags: ["tickets"],
		})
		.input(IdInputSchema)
		.handler(async ({ input }) => {
			return await prisma.supportTicket.findUnique({
				where: {
					id: input.id,
				},
			});
		}),
	create: protectedProcedure
		.route({
			method: "POST",
			path: "/tickets",
			summary: "Create ticket",
			description: "Create ticket",
			tags: ["tickets"],
		})
		.input(CreateTicketInputSchema)
		.handler(async ({ input }) => {
			function generateTicketId() {
				const characters = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
				let result = "SNAP-";
				const year = new Date().getFullYear().toString().slice(-2);
				result += `${year}-`;
				for (let i = 0; i < 3; i++) {
					result += characters.charAt(
						Math.floor(Math.random() * characters.length),
					);
				}
				return result;
			}
			const ticketId = generateTicketId();
			return await prisma.supportTicket.create({
				data: {
					customerEmail: input.customerEmail,
					customerName: input.customerName,
					licenseKey: input.licenseKey,
					subject: input.subject,
					description: input.description,
					category: input.category,
					priority: input.priority,
					status: input.status,
					assignedTo: input.assignedTo,
					ticketId: ticketId,
				},
			});
		}),
	update: protectedProcedure
		.route({
			method: "PUT",
			path: "/tickets",
			summary: "Update ticket",
			description: "Update ticket",
			tags: ["tickets"],
		})
		.input(UpdateTicketInputSchema)
		.handler(async ({ input }) => {
			return await prisma.supportTicket.update({
				where: {
					id: input.id,
				},
				data: {
					priority: input.priority,
					status: input.status,
					assignedTo: input.assignedTo,
				},
			});
		}),

	delete: protectedProcedure
		.route({
			method: "DELETE",
			path: "/tickets/{id}",
			summary: "Delete ticket",
			description: "Delete ticket",
			tags: ["tickets"],
		})
		.input(IdInputSchema)
		.handler(async ({ input }) => {
			return await prisma.supportTicket.delete({
				where: {
					id: input.id,
				},
			});
		}),
};
