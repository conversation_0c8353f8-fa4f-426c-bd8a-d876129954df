import {
	AuditLogPaginatedOutputSchema,
	IdInputSchema,
	PaginateInputSchema,
	prisma,
} from "@repo/db";
import { protectedProcedure } from "@/lib/orpc";
import { buildPaginationResponse } from "@/lib/pagination";

export const logs = {
	list: protectedProcedure
		.route({
			method: "GET",
			path: "/logs",
			summary: "List logs",
			description: "List logs",
			tags: ["logs"],
		})
		.handler(async ({ context }) => {
			return await prisma.auditLog.findMany();
		}),
	paginate: protectedProcedure
		.route({
			method: "GET",
			path: "/logs/paginate",
			summary: "Paginated logs",
			description: "Paginated logs",
			tags: ["logs"],
		})
		.input(PaginateInputSchema)
		.output(AuditLogPaginatedOutputSchema)
		.handler(async ({ input }) => {
			return await buildPaginationResponse(prisma.auditLog, input);
		}),

	get: protectedProcedure
		.route({
			method: "GET",
			path: "/logs/{id}",
			summary: "Get log",
			description: "Get log",
			tags: ["logs"],
		})
		.input(IdInputSchema)
		.handler(async ({ input }) => {
			return await prisma.auditLog.findUnique({
				where: {
					id: input.id,
				},
			});
		}),
};
