import {
	CreateInvitationInputSchema,
	IdInputSchema,
	InvitationPaginatedOutputSchema,
	PaginateInputSchema,
	prisma,
	UpdateInvitationInputSchema,
} from "@repo/db";

import { protectedProcedure } from "@/lib/orpc";
import { buildPaginationResponse } from "@/lib/pagination";

export const invitations = {
	list: protectedProcedure
		.route({
			method: "GET",
			path: "/invitations",
			summary: "List invitations",
			description: "List invitations",
			tags: ["invitations"],
		})
		.handler(async ({ context }) => {
			return await prisma.userInvitation.findMany();
		}),
	paginate: protectedProcedure
		.route({
			method: "GET",
			path: "/invitations/paginate",
			summary: "Paginated invitations",
			description: "Paginated invitations",
			tags: ["invitations"],
		})
		.input(PaginateInputSchema)
		.output(InvitationPaginatedOutputSchema)
		.handler(async ({ input }) => {
			return await buildPaginationResponse(prisma.userInvitation, input);
		}),

	get: protectedProcedure
		.route({
			method: "GET",
			path: "/invitations/{id}",
			summary: "Get invitation",
			description: "Get invitation",
			tags: ["invitations"],
		})
		.input(IdInputSchema)
		.handler(async ({ input }) => {
			return await prisma.userInvitation.findUnique({
				where: {
					id: input.id,
				},
			});
		}),

	create: protectedProcedure
		.route({
			method: "POST",
			path: "/invitations",
			summary: "Create invitation",
			description: "Create invitation",
			tags: ["invitations"],
		})
		.input(CreateInvitationInputSchema)
		.handler(async ({ input }) => {
			return await prisma.userInvitation.create({
				data: {
					email: input.email,
					role: input.role,
					sentBy: input.sentBy,
					token: input.token,
					expiresAt: input.expiresAt,
				},
			});
		}),

	update: protectedProcedure
		.route({
			method: "PUT",
			path: "/invitations",
			summary: "Update invitation",
			description: "Update invitation",
			tags: ["invitations"],
		})
		.input(UpdateInvitationInputSchema)
		.handler(async ({ input }) => {
			return await prisma.userInvitation.update({
				where: {
					id: input.id,
				},
				data: {
					status: input.status,
				},
			});
		}),

	delete: protectedProcedure
		.route({
			method: "DELETE",
			path: "/invitations/{id}",
			summary: "Delete invitation",
			description: "Delete invitation",
			tags: ["invitations"],
		})
		.input(IdInputSchema)
		.handler(async ({ input }) => {
			return await prisma.userInvitation.delete({
				where: {
					id: input.id,
				},
			});
		}),
};
