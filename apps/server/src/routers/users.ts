import {
	CreateUserInputSchema,
	IdInputSchema,
	PaginateInputSchema,
	prisma,
	UpdateUserInputSchema,
	UserPaginatedOutputSchema,
} from "@repo/db";

import { protectedProcedure } from "@/lib/orpc";
import { buildPaginationResponse } from "@/lib/pagination";

export const users = {
	getMe: protectedProcedure
		.route({
			method: "GET",
			path: "/users/me",
			summary: "Get me",
			description: "Get me",
			tags: ["users"],
		})
		.handler(({ context }) => {
			return context.session?.user;
		}),

	list: protectedProcedure
		.route({
			method: "GET",
			path: "/users",
			summary: "List users",
			description: "List users",
			tags: ["users"],
		})
		.handler(async ({ context }) => {
			return await prisma.user.findMany({
				where: {
					NOT: { id: { equals: context.session?.user.id } },
				},
			});
		}),

	paginate: protectedProcedure
		.route({
			method: "GET",
			path: "/users/paginate",
			summary: "Paginated users",
			description: "Paginated users",
			tags: ["users"],
		})
		.input(PaginateInputSchema)
		.output(UserPaginatedOutputSchema)
		.handler(async ({ input, context }) => {
			const { search, filters } = input;

			let where: Record<string, unknown> = {
				NOT: { id: { equals: context.session?.user.id } },
			};

			if (search) {
				where = {
					...where,
					OR: [
						{ name: { contains: search, mode: "insensitive" } },
						{ email: { contains: search, mode: "insensitive" } },
					],
					...filters,
				};
			}

			return await buildPaginationResponse(prisma.user, input, where);
		}),

	get: protectedProcedure
		.route({
			method: "GET",
			path: "/users/{id}",
			summary: "Get user",
			description: "Get user",
			tags: ["users"],
		})
		.input(IdInputSchema)
		.handler(async ({ input }) => {
			return await prisma.user.findUnique({
				where: {
					id: input.id,
				},
			});
		}),

	create: protectedProcedure
		.route({
			method: "POST",
			path: "/users",
			summary: "Create user",
			description: "Create user",
			tags: ["users"],
		})
		.input(CreateUserInputSchema)
		.handler(async ({ input }) => {
			return await prisma.user.create({
				data: {
					email: input.email,
					name: input.name,
					role: input.role,
				},
			});
		}),
	update: protectedProcedure
		.route({
			method: "PUT",
			path: "/users",
			summary: "Update user",
			description: "Update user",
			tags: ["users"],
		})
		.input(UpdateUserInputSchema)
		.handler(async ({ input }) => {
			return await prisma.user.update({
				where: {
					id: input.id,
				},
				data: {
					role: input.role,
					isActive: input.isActive,
				},
			});
		}),

	delete: protectedProcedure
		.route({
			method: "DELETE",
			path: "/users/{id}",
			summary: "Delete user",
			description: "Delete user",
			tags: ["users"],
		})
		.input(IdInputSchema)
		.handler(async ({ input }) => {
			return await prisma.user.delete({
				where: {
					id: input.id,
				},
			});
		}),
};
