import {
	IdInputSchema,
	PaginateInputSchema,
	prisma,
	RateLimitPaginatedOutputSchema,
} from "@repo/db";

import { protectedProcedure } from "@/lib/orpc";
import { buildPaginationResponse } from "@/lib/pagination";

export const rateLimits = {
	list: protectedProcedure
		.route({
			method: "GET",
			path: "/rate-limits",
			summary: "List rate limits",
			description: "List rate limits",
			tags: ["rate-limits"],
		})
		.handler(async ({ context }) => {
			return await prisma.rateLimit.findMany();
		}),
	paginate: protectedProcedure
		.route({
			method: "GET",
			path: "/rate-limits/paginate",
			summary: "Paginated rate limits",
			description: "Paginated rate limits",
			tags: ["rate-limits"],
		})
		.input(PaginateInputSchema)
		.output(RateLimitPaginatedOutputSchema)
		.handler(async ({ input }) => {
			return await buildPaginationResponse(prisma.rateLimit, input);
		}),

	get: protectedProcedure
		.route({
			method: "GET",
			path: "/rate-limits/{id}",
			summary: "Get rate limit",
			description: "Get rate limit",
			tags: ["rate-limits"],
		})
		.input(IdInputSchema)
		.handler(async ({ input }) => {
			return await prisma.rateLimit.findUnique({
				where: {
					id: input.id,
				},
			});
		}),
};
