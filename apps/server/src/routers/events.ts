import {
	EventPaginatedOutputSchema,
	IdInputSchema,
	PaginateInputSchema,
	prisma,
} from "@repo/db";
import { protectedProcedure } from "@/lib/orpc";
import { buildPaginationResponse } from "@/lib/pagination";

export const events = {
	list: protectedProcedure
		.route({
			method: "GET",
			path: "/events",
			summary: "List events",
			description: "List events",
			tags: ["events"],
		})
		.handler(async ({ context }) => {
			return await prisma.webhookEvent.findMany();
		}),
	paginate: protectedProcedure
		.route({
			method: "GET",
			path: "/events/paginate",
			summary: "Paginated events",
			description: "Paginated events",
			tags: ["events"],
		})
		.input(PaginateInputSchema)
		.output(EventPaginatedOutputSchema)
		.handler(async ({ input }) => {
			return await buildPaginationResponse(prisma.webhookEvent, input);
		}),

	get: protectedProcedure
		.route({
			method: "GET",
			path: "/events/{id}",
			summary: "Get event",
			description: "Get event",
			tags: ["events"],
		})
		.input(IdInputSchema)
		.handler(async ({ input }) => {
			return await prisma.webhookEvent.findUnique({
				where: {
					id: input.id,
				},
			});
		}),
};
