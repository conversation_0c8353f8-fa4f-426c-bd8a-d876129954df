#!/usr/bin/env bun
/**
 * Webhook retry cron job
 *
 * This script should be run periodically (e.g., every 5 minutes) to retry failed webhook events.
 *
 * Usage:
 * bun run src/scripts/webhook-retry-cron.ts
 *
 * Or add to crontab:
 * *\/5 * * * * cd /path/to/your/app && bun run src/scripts/webhook-retry-cron.ts'
 */

import "dotenv/config";
import { webhookRetryService } from "../services/webhook-retry";

async function main() {
	console.log("Starting webhook retry cron job...");

	try {
		// Retry failed webhook events
		await webhookRetryService.retryFailedEvents();

		// Get and log statistics
		const stats = await webhookRetryService.getWebhookStats();
		console.log("Webhook statistics:", stats);

		// Clean up old events (run less frequently)
		const shouldCleanup = Math.random() < 0.1; // 10% chance
		if (shouldCleanup) {
			console.log("Running cleanup of old webhook events...");
			const cleanedCount = await webhookRetryService.cleanupOldEvents();
			console.log(`Cleaned up ${cleanedCount} old webhook events`);
		}

		console.log("Webhook retry cron job completed successfully");
		process.exit(0);
	} catch (error) {
		console.error("Webhook retry cron job failed:", error);
		process.exit(1);
	}
}

main();
