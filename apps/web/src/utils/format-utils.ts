import { format } from "date-fns";

function capitalize(str: string) {
	if (!str) return "";
	return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
}

function formatCurrency(amount: number) {
	return new Intl.NumberFormat("en-US", {
		style: "currency",
		currency: "USD",
	}).format(amount);
}

function uppercase(str: string) {
	if (!str) return "";
	return str.toUpperCase();
}

function formatDate(date: Date) {
	return format(date, "MM/dd/yyyy");
}

export { capitalize, formatDate, formatCurrency, uppercase };
