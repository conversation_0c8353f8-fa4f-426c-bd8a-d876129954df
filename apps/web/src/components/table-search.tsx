import { IconSearch, IconX } from "@tabler/icons-react";
import type { Table } from "@tanstack/react-table";
import { Loader2 } from "lucide-react";
import type React from "react";
import { useId } from "react";
import { Input } from "./ui/input";

interface TableSearchProps {
	placeholder?: string;
	value: string;
	onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
	onClear: () => void;
	isLoading?: boolean;
}
export default function TableSearch({
	placeholder = "Search...",
	value,
	onChange,
	onClear,
	isLoading = false,
}: TableSearchProps) {
	return (
		<div className="relative w-64">
			<IconSearch className="-translate-y-1/2 absolute top-1/2 left-2 size-4 text-muted-foreground" />
			<Input
				id={useId()}
				placeholder={placeholder}
				value={value}
				onChange={onChange}
				className="w-64 pr-8 pl-8"
			/>

			{isLoading && (
				<div className="-translate-y-1/2 absolute top-1/2 right-2">
					<Loader2 className="size-4 text-muted-foreground" />
				</div>
			)}
			{value && (
				<button
					type="button"
					onClick={onClear}
					className="-translate-y-1/2 absolute top-1/2 right-2"
				>
					<IconX className="size-4 text-muted-foreground" />
				</button>
			)}
		</div>
	);
}
