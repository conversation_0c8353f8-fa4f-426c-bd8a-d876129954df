import {
	closestCenter,
	DndContext,
	type Drag<PERSON>ndEvent,
	KeyboardSensor,
	MouseSensor,
	TouchSensor,
	type UniqueIdentifier,
	useSensor,
	useSensors,
} from "@dnd-kit/core";
import { restrictToVerticalAxis } from "@dnd-kit/modifiers";
import {
	arrayMove,
	SortableContext,
	useSortable,
	verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { IconPlus } from "@tabler/icons-react";
import {
	type ColumnDef,
	flexRender,
	getCoreRowModel,
	getSortedRowModel,
	type OnChangeFn,
	type Row,
	type RowSelectionState,
	useReactTable,
} from "@tanstack/react-table";
import { useEffect, useId, useMemo, useState } from "react";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from "@/components/ui/table";
import TablePagination from "./table-pagination";
import TableSearch from "./table-search";
import { <PERSON><PERSON> } from "./ui/button";

// Sorting
type ColumnSort = {
	id: string;
	desc: boolean;
};
type SortingState = ColumnSort[];

// Pagination
type PaginationMeta = {
	totalCount: number;
	totalPages: number;
	page: number;
	limit: number;
	nextPage?: number;
	previousPage?: number;
};

export interface PaginationParams {
	page?: number;
	limit?: number;
	sortBy?: string;
	sortOrder?: "asc" | "desc";
	filters?: Record<string, any>;
	search?: string;
}

interface CustomTableProps<T> {
	initialData: { items: T[] | undefined; meta: PaginationMeta } | undefined;
	columns: ColumnDef<T>[];
	onPageChange: (page: number) => void;
	onPageSizeChange: (size: number) => void;
	onRowSelectionChange: OnChangeFn<RowSelectionState>;
	rowSelection: RowSelectionState;
	sorting: SortingState;
	onSortingChange: OnChangeFn<SortingState>;
	onGlobalFilterChange: OnChangeFn<any>;
	globalFilterPlaceholder: string;
	globalFilter: any;
	isLoading: boolean;
	onAddClick: () => void;
	addButtonText: string;
}

function DraggableRow<T extends { id: UniqueIdentifier }>({
	row,
}: {
	row: Row<T>;
}) {
	const { transform, transition, setNodeRef, isDragging } = useSortable({
		id: row.original.id,
	});

	return (
		<TableRow
			data-state={row.getIsSelected() && "selected"}
			data-dragging={isDragging}
			ref={setNodeRef}
			className="relative z-0 data-[dragging=true]:z-10 data-[dragging=true]:opacity-80"
			style={{
				transform: CSS.Transform?.toString(transform),
				transition: transition,
			}}
		>
			{row.getVisibleCells().map((cell) => (
				<TableCell key={cell.id}>
					{flexRender(cell.column.columnDef.cell, cell.getContext())}
				</TableCell>
			))}
		</TableRow>
	);
}
export default function CustomTable<T extends { id: UniqueIdentifier }>({
	initialData,
	columns,
	onPageChange,
	onPageSizeChange,
	onRowSelectionChange,
	rowSelection,
	sorting,
	onSortingChange,
	onGlobalFilterChange,
	globalFilterPlaceholder,
	globalFilter,
	isLoading,
	onAddClick,
	addButtonText,
}: CustomTableProps<T>) {
	const [data, setData] = useState<T[]>(initialData?.items || []);

	const sortableId = useId();
	const sensors = useSensors(
		useSensor(MouseSensor, {}),
		useSensor(TouchSensor, {}),
		useSensor(KeyboardSensor, {}),
	);

	useEffect(() => {
		if (initialData) {
			setData(initialData.items || []);
		}
	}, [initialData]);

	const dataIds = useMemo<UniqueIdentifier[]>(
		() => data?.map(({ id }) => id) || [],
		[data],
	);

	const table = useReactTable({
		columns,
		data,
		onRowSelectionChange: onRowSelectionChange,
		state: {
			sorting,
			rowSelection,
		},
		// sorting
		onSortingChange: onSortingChange,
		getCoreRowModel: getCoreRowModel(),
		getSortedRowModel: getSortedRowModel(),
		// drag and drop
		getRowId: (row) => row.id?.toString(),
		manualSorting: true,
		// global filter(search)
		manualFiltering: true,
	});

	function handleDragEnd(event: DragEndEvent) {
		const { active, over } = event;
		if (active && over && active.id !== over.id) {
			setData((data) => {
				const oldIndex = dataIds.indexOf(active.id);
				const newIndex = dataIds.indexOf(over.id);
				return arrayMove(data, oldIndex, newIndex);
			});
		}
	}

	return (
		<div className="flex w-full flex-col gap-4">
			{/* Search */}
			<div className="flex items-center justify-between">
				<TableSearch
					value={globalFilter}
					onChange={(e) => onGlobalFilterChange(String(e.target.value))}
					placeholder={globalFilterPlaceholder}
					onClear={() => onGlobalFilterChange("")}
					isLoading={isLoading}
				/>
				<Button variant="outline" size="default" onClick={onAddClick}>
					<IconPlus />
					<span className="hidden lg:inline">{addButtonText}</span>
				</Button>
			</div>

			<div className="overflow-hidden rounded-lg border">
				<DndContext
					collisionDetection={closestCenter}
					modifiers={[restrictToVerticalAxis]}
					onDragEnd={handleDragEnd}
					sensors={sensors}
					id={sortableId}
				>
					<Table>
						<TableHeader className="sticky top-0 z-10 bg-muted">
							{table.getHeaderGroups().map((headerGroup) => (
								<TableRow key={headerGroup.id}>
									{headerGroup.headers.map((header) => {
										return (
											<TableHead key={header.id} colSpan={header.colSpan}>
												{header.isPlaceholder
													? null
													: flexRender(
															header.column.columnDef.header,
															header.getContext(),
														)}
											</TableHead>
										);
									})}
								</TableRow>
							))}
						</TableHeader>
						<TableBody className="**:data-[slot=table-cell]:first:w-8">
							{table.getRowModel().rows?.length ? (
								<SortableContext
									items={dataIds}
									strategy={verticalListSortingStrategy}
								>
									{table.getRowModel().rows.map((row) => (
										<DraggableRow key={row.id} row={row} />
									))}
								</SortableContext>
							) : (
								<TableRow>
									<TableCell
										colSpan={columns.length}
										className="h-24 text-center"
									>
										No results.
									</TableCell>
								</TableRow>
							)}
						</TableBody>
					</Table>
				</DndContext>
			</div>
			{/* Pagination */}
			<TablePagination
				visibleRows={initialData?.meta.totalCount as number}
				selectedRows={Object.keys(table.getSelectedRowModel().rows).length}
				page={initialData?.meta.page as number}
				totalPages={initialData?.meta.totalPages as number}
				onPageChange={onPageChange}
				isNextPageDisabled={!initialData?.meta.nextPage}
				isPreviousPageDisabled={!initialData?.meta.previousPage}
				isFirstPageDisabled={initialData?.meta.page === 1}
				isLastPageDisabled={
					initialData?.meta.page === initialData?.meta.totalPages
				}
				pageSize={initialData?.meta.limit as number}
				onPageSizeChange={onPageSizeChange}
			/>
		</div>
	);
}
