import {
	IconChevronLeft,
	IconChevronRight,
	IconChevronsLeft,
	IconChevronsRight,
} from "@tabler/icons-react";

import { Button } from "./ui/button";
import { Label } from "./ui/label";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "./ui/select";

interface TablePaginationProps {
	page: number;
	totalPages: number;
	onPageChange: (page: number) => void;
	isNextPageDisabled: boolean;
	isPreviousPageDisabled: boolean;
	isFirstPageDisabled: boolean;
	isLastPageDisabled: boolean;
	pageSize: number;
	onPageSizeChange: (size: number) => void;
	pageSizeOptions?: number[];
	selectedRows: number;
	visibleRows: number;
}
export default function TablePagination({
	page,
	totalPages,
	onPageChange,
	isNextPageDisabled,
	isPreviousPageDisabled,
	isFirstPageDisabled,
	isLastPageDisabled,
	pageSize,
	onPageSizeChange,
	pageSizeOptions = [10, 20, 30, 40, 50],
	selectedRows,
	visibleRows,
}: TablePaginationProps) {
	return (
		<div className="flex items-center justify-between">
			<div className="hidden flex-1 text-muted-foreground text-sm lg:flex">
				{selectedRows} of {visibleRows} row(s) selected.
			</div>
			<div className="flex w-full items-center gap-8 lg:w-fit">
				<div className="hidden items-center gap-2 lg:flex">
					<Label htmlFor="rows-per-page" className="font-medium text-sm">
						Rows per page
					</Label>
					<Select
						value={pageSize ? pageSize.toString() : ""}
						onValueChange={(value) => {
							onPageSizeChange(Number(value));
						}}
					>
						{/** biome-ignore lint/correctness/useUniqueElementIds: matches htmlFor */}
						<SelectTrigger size="sm" className="w-20" id="rows-per-page">
							<SelectValue placeholder={pageSize ? pageSize.toString() : ""} />
						</SelectTrigger>
						<SelectContent side="top">
							{pageSizeOptions.map((pageSize) => (
								<SelectItem
									key={pageSize}
									value={pageSize ? pageSize.toString() : ""}
								>
									{pageSize}
								</SelectItem>
							))}
						</SelectContent>
					</Select>
				</div>
				<div className="flex w-fit items-center justify-center font-medium text-sm">
					Page {page} of {totalPages}
				</div>
				<div className="ml-auto flex items-center gap-2 lg:ml-0">
					<Button
						variant="outline"
						className="hidden h-8 w-8 p-0 lg:flex"
						onClick={() => {
							onPageChange(1);
						}}
						disabled={isFirstPageDisabled}
					>
						<span className="sr-only">Go to first page</span>
						<IconChevronsLeft />
					</Button>
					<Button
						variant="outline"
						className="size-8"
						size="icon"
						onClick={() => {
							onPageChange(page - 1);
						}}
						disabled={isPreviousPageDisabled}
					>
						<span className="sr-only">Go to previous page</span>
						<IconChevronLeft />
					</Button>
					<Button
						variant="outline"
						className="size-8"
						size="icon"
						onClick={() => {
							onPageChange(page + 1);
						}}
						disabled={isNextPageDisabled}
					>
						<span className="sr-only">Go to next page</span>
						<IconChevronRight />
					</Button>
					<Button
						variant="outline"
						className="hidden size-8 lg:flex"
						size="icon"
						onClick={() => {
							onPageChange(totalPages);
						}}
						disabled={isLastPageDisabled}
					>
						<span className="sr-only">Go to last page</span>
						<IconChevronsRight />
					</Button>
				</div>
			</div>
		</div>
	);
}
