import type { AuditLogType } from "@repo/db";
import { useQuery } from "@tanstack/react-query";
import { createFileRoute } from "@tanstack/react-router";
import type { Column, Row, Table } from "@tanstack/react-table";
import { useMemo } from "react";
import CustomTable from "@/components/custom-table";
import { DragHandle } from "@/components/drag-handle";
import TableShowHideColumns from "@/components/table-show-hide-columns";
import SortableHeader from "@/components/table-sortable-header";
import { Badge } from "@/components/ui/badge";
import useTable from "@/hooks/use-table";
import { formatDate } from "@/utils/format-utils";
import { orpc } from "@/utils/orpc";
export const Route = createFileRoute("/app/audit-logs")({
	component: LogsPage,
});

function LogsPage() {
	const {
		page,
		limit,
		globalFilter,
		rowSelection,
		sorting,
		setPage,
		handlePageSizeChange,
		handleGlobalFilterChange,
		setRowSelection,
		setSorting,
		search,
	} = useTable();

	const logs = useQuery(
		orpc.logs.paginate.queryOptions({
			input: {
				page,
				limit,
				sortBy: sorting[0]?.id,
				sortOrder: sorting[0]?.desc ? "desc" : "asc",
				search: globalFilter ? search.toLowerCase() : globalFilter,
			},
		}),
	);

	const columns = useMemo(
		() => [
			{
				id: "drag",
				header: () => null,
				cell: ({ row }: { row: Row<AuditLogType> }) => (
					<DragHandle id={row.original.id} />
				),
			},
			{
				accessorKey: "userEmail",
				enableSorting: true,
				enableHiding: false,
				header: ({ column }: { column: Column<AuditLogType> }) => (
					<SortableHeader column={column} label="User Email" />
				),
			},

			{
				accessorKey: "customerEmail",
				enableSorting: true,
				header: ({ column }: { column: Column<AuditLogType> }) => (
					<SortableHeader column={column} label="Customer Email" />
				),
			},
			{
				accessorKey: "action",
				enableSorting: true,
				header: ({ column }: { column: Column<AuditLogType> }) => (
					<SortableHeader column={column} label="Action" />
				),
				cell: ({ row }: { row: Row<AuditLogType> }) => (
					<Badge variant="outline" className="px-1.5 text-muted-foreground">
						{row.original.action}
					</Badge>
				),
			},
			{
				accessorKey: "createdAt",
				enableSorting: true,
				header: ({ column }: { column: Column<AuditLogType> }) => (
					<SortableHeader column={column} label="Created At" />
				),
				cell: ({ row }: { row: Row<AuditLogType> }) => (
					<span>{formatDate(row.original.createdAt)}</span>
				),
			},

			{
				accessorKey: "ipAddress",
				enableSorting: true,
				header: ({ column }: { column: Column<AuditLogType> }) => (
					<SortableHeader column={column} label="IP Address" />
				),
				cell: ({ row }: { row: Row<AuditLogType> }) => (
					<span className="rounded-sm bg-muted p-1 font-mono">
						{row.original.ipAddress}
					</span>
				),
			},
			{
				id: "actions",
				header: ({ table }: { table: Table<AuditLogType> }) => (
					<TableShowHideColumns table={table} />
				),
			},
		],
		[],
	);

	return (
		<div className="px-4 py-4">
			<h1 className="mb-4 font-bold text-2xl">Audit Logs</h1>
			<CustomTable
				isLoading={logs.isPending}
				initialData={logs.data}
				columns={columns}
				onPageChange={(page: number) => setPage(page)}
				onPageSizeChange={handlePageSizeChange}
				onRowSelectionChange={setRowSelection}
				rowSelection={rowSelection}
				sorting={sorting}
				onSortingChange={setSorting}
				globalFilter={globalFilter}
				globalFilterPlaceholder="Search logs..."
				onGlobalFilterChange={handleGlobalFilterChange}
				onAddClick={() => console.log("Add")}
				addButtonText="Add Log"
			/>
		</div>
	);
}
