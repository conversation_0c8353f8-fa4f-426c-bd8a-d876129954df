import type { UserType } from "@repo/db";
import { IconCircleCheckFilled, IconCircleXFilled } from "@tabler/icons-react";
import { useQuery } from "@tanstack/react-query";
import { createFileRoute } from "@tanstack/react-router";
import type { Column, Row, Table } from "@tanstack/react-table";
import { Award } from "lucide-react";
import { useMemo } from "react";
import CustomTable from "@/components/custom-table";
import { DragHandle } from "@/components/drag-handle";
import TableActions from "@/components/table-actions";
import TableShowHideColumns from "@/components/table-show-hide-columns";
import SortableHeader from "@/components/table-sortable-header";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import useTable from "@/hooks/use-table";
import { orpc } from "@/utils/orpc";
export const Route = createFileRoute("/app/users")({
	component: UsersPage,
});

function UsersPage() {
	const {
		page,
		limit,
		globalFilter,
		rowSelection,
		sorting,
		handlePageSizeChange,
		handleGlobalFilterChange,
		handlePageChange,
		setRowSelection,
		setSorting,
		search,
	} = useTable();

	const { isPending, data: users } = useQuery(
		orpc.users.paginate.queryOptions({
			input: {
				page,
				limit,
				sortBy: sorting[0]?.id,
				sortOrder: sorting[0]?.desc ? "desc" : "asc",
				search: globalFilter ? search.toLowerCase() : globalFilter,
			},
		}),
	);

	const columns = useMemo(
		() => [
			{
				id: "drag",
				header: () => null,
				cell: ({ row }: { row: Row<UserType> }) => (
					<DragHandle id={row.original.id} />
				),
			},
			{
				id: "select",
				header: ({ table }: { table: Table<UserType> }) => (
					<div className="flex items-center justify-center">
						<Checkbox
							checked={
								table.getIsAllPageRowsSelected() ||
								(table.getIsSomePageRowsSelected() && "indeterminate")
							}
							onCheckedChange={(value) =>
								table.toggleAllPageRowsSelected(!!value)
							}
							aria-label="Select all"
						/>
					</div>
				),
				cell: ({ row }: { row: Row<UserType> }) => (
					<div className="flex items-center justify-center">
						<Checkbox
							checked={row.getIsSelected()}
							onCheckedChange={(value) => row.toggleSelected(!!value)}
							aria-label="Select row"
						/>
					</div>
				),
				enableSorting: false,
				enableHiding: false,
			},
			{
				accessorKey: "name",
				enableSorting: true,
				header: ({ column }: { column: Column<UserType> }) => (
					<SortableHeader column={column} label="Name" />
				),
			},
			{
				accessorKey: "email",
				enableSorting: true,
				enableHiding: false,
				header: ({ column }: { column: Column<UserType> }) => (
					<SortableHeader column={column} label="Email" />
				),
			},
			{
				accessorKey: "role",
				enableSorting: true,
				header: ({ column }: { column: Column<UserType> }) => (
					<SortableHeader column={column} label="Role" />
				),
				cell: ({ row }: { row: Row<UserType> }) => (
					<Badge
						variant="outline"
						className="px-1.5 text-muted-foreground capitalize"
					>
						<Award />
						{row.original.role}
					</Badge>
				),
			},
			{
				accessorKey: "isActive",
				enableSorting: true,
				header: ({ column }: { column: Column<UserType> }) => (
					<SortableHeader column={column} label="Status" />
				),
				cell: ({ row }: { row: Row<UserType> }) => (
					<Badge
						variant="outline"
						className="px-1.5 text-muted-foreground capitalize"
					>
						{row.original.isActive ? (
							<IconCircleCheckFilled className="fill-green-500 dark:fill-green-400" />
						) : (
							<IconCircleXFilled className="fill-red-500 dark:fill-red-400" />
						)}
						{row.original.isActive ? "ACTIVE" : "INACTIVE"}
					</Badge>
				),
			},
			{
				id: "actions",
				header: ({ table }: { table: Table<UserType> }) => (
					<TableShowHideColumns table={table} />
				),
				cell: () => (
					<TableActions
						onEdit={() => console.log("Edit")}
						onDelete={() => console.log("Delete")}
					/>
				),
			},
		],
		[],
	);

	return (
		<div className="px-4 py-4">
			<h1 className="mb-4 font-bold text-2xl">Team</h1>
			<CustomTable
				isLoading={isPending}
				initialData={users}
				columns={columns}
				onPageChange={handlePageChange}
				onPageSizeChange={handlePageSizeChange}
				onRowSelectionChange={setRowSelection}
				rowSelection={rowSelection}
				sorting={sorting}
				onSortingChange={setSorting}
				globalFilter={globalFilter}
				globalFilterPlaceholder="Search members..."
				onGlobalFilterChange={handleGlobalFilterChange}
				onAddClick={() => console.log("Add")}
				addButtonText="Add Member"
			/>
		</div>
	);
}
