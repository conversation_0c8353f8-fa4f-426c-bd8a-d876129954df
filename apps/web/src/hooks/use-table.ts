import type { RowSelectionState, SortingState } from "@tanstack/react-table";
import { useState } from "react";
import { useDebounce } from "use-debounce";

export default function useTable() {
	const [page, setPage] = useState<number | undefined>(1);
	const [limit, setLimit] = useState<number | undefined>(10);
	const [globalFilter, setGlobalFilter] = useState<string>("");
	const [rowSelection, setRowSelection] = useState<RowSelectionState>({});
	const [sorting, setSorting] = useState<SortingState>([]);
	const [search] = useDebounce(globalFilter, 1000);

	const handlePageSizeChange = (size: number) => {
		setLimit(size);
		setPage(1);
	};
	const handleGlobalFilterChange = (value: string) => {
		setGlobalFilter(value);
		setPage(1);
	};

	const handlePageChange = (page: number) => {
		setPage(page);
	};

	return {
		page,
		limit,
		globalFilter,
		rowSelection,
		sorting,
		search,
		setPage,
		setLimit,
		setGlobalFilter,
		setRowSelection,
		setSorting,
		handlePageSizeChange,
		handleGlobalFilterChange,
		handlePageChange,
	};
}
